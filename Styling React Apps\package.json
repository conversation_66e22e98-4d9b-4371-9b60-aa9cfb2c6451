{"name": "styling", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "styled-components": "^6.1.18"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^4.0.1", "autoprefixer": "^10.4.21", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.4.19"}}